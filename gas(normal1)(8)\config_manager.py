"""
配置管理模块
负责处理应用程序的配置文件、历史文件路径、主题设置等
"""
import os
import json
from datetime import datetime
import shutil
import tkinter as tk
from tkinter import ttk, messagebox, filedialog


class ConfigManager:
    def __init__(self, parent):
        self.parent = parent
        self.app_data_dir = os.path.join(os.environ.get('APPDATA', os.path.expanduser('~')), 'GasCalculator')
        if not os.path.exists(self.app_data_dir):
            os.makedirs(self.app_data_dir)
        
        # 默认历史文件路径
        self.history_file = os.path.join(self.app_data_dir, 'history.json')
        
        # 配置文件路径
        self.config_file = os.path.join(self.app_data_dir, 'config.json')
        
        # 加载配置
        self.load_config()
        
        # 根据配置文件设置历史文件路径
        if hasattr(self, 'custom_history_path') and self.custom_history_path and os.path.exists(os.path.dirname(self.custom_history_path)):
            self.history_file = self.custom_history_path

    def backup_history_file(self):
        """创建历史文件的备份"""
        try:
            if os.path.exists(self.history_file):
                backup_dir = os.path.join(os.path.dirname(self.history_file), "备份")
                if not os.path.exists(backup_dir):
                    os.makedirs(backup_dir)

                today = datetime.now().strftime("%Y%m%d")
                backup_file = os.path.join(backup_dir, f"history_{today}.json")

                shutil.copy2(self.history_file, backup_file)
        except Exception as e:
            print(f"创建历史文件备份时出错: {str(e)}")

    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self.custom_history_path = config.get('history_file_path', '')
            else:
                self.custom_history_path = ''
        except Exception as e:
            print(f"加载配置文件出错: {str(e)}")
            self.custom_history_path = ''

    def save_config(self):
        """保存配置文件"""
        try:
            config = {}
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

            # 更新历史文件路径
            config['history_file_path'] = self.custom_history_path if hasattr(self, 'custom_history_path') else ''

            # 保存配置文件
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存配置文件出错: {str(e)}")

    def save_theme_config(self, theme_name):
        """保存主题设置到配置文件"""
        try:
            config_dir = os.path.dirname(self.history_file)
            if not os.path.exists(config_dir):
                os.makedirs(config_dir)
            
            theme_config_file = os.path.join(config_dir, 'theme_config.json')
            
            config = {}
            if os.path.exists(theme_config_file):
                with open(theme_config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
            
            config['current_theme'] = theme_name
            
            with open(theme_config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"保存主题配置时出错: {str(e)}")

    def save_window_size_config(self, width_percent, height_percent):
        """保存窗口大小设置到配置文件"""
        try:
            config_dir = os.path.dirname(self.history_file)
            if not os.path.exists(config_dir):
                os.makedirs(config_dir)
            
            window_config_file = os.path.join(config_dir, 'window_config.json')
            config = {
                'width_percent': width_percent,
                'height_percent': height_percent
            }
            
            with open(window_config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"保存窗口配置时出错: {str(e)}")

    def load_window_size_config(self):
        """加载窗口大小设置"""
        try:
            config_dir = os.path.dirname(self.history_file)
            window_config_file = os.path.join(config_dir, 'window_config.json')
            
            if os.path.exists(window_config_file):
                with open(window_config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    
                width_percent = config.get('width_percent', 80)
                height_percent = config.get('height_percent', 100)
                
                # 获取屏幕尺寸
                screen_width = self.parent.root.winfo_screenwidth()
                screen_height = self.parent.root.winfo_screenheight()
                
                # 计算窗口尺寸
                window_width = int(screen_width * width_percent / 100)
                window_height = int(screen_height * height_percent / 100)
                
                # 计算窗口位置，使其居中显示
                x = (screen_width - window_width) // 2
                y = (screen_height - window_height) // 2
                
                # 设置窗口大小和位置
                self.parent.root.geometry(f"{window_width}x{window_height}+{x}+{y}")
                
                print(f"已加载窗口配置: {width_percent}% x {height_percent}%")
                return True
                
        except Exception as e:
            print(f"加载窗口配置时出错: {str(e)}")
        
        return False

    def center_window(self, window):
        """使窗口在屏幕中居中显示"""
        window.update_idletasks()
        width = window.winfo_width()
        height = window.winfo_height()
        x = (window.winfo_screenwidth() // 2) - (width // 2)
        y = (window.winfo_screenheight() // 2) - (height // 2)
        window.geometry(f'{width}x{height}+{x}+{y}')
